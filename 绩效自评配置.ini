[基本信息]
姓名=雷雨
部门=智能创新
职位=AI工程师
评估周期=半年度/年度

[话术风格特征]
# 从2024年绩效评估中提炼的话术风格
关键词=紧跟团队步伐,快步尝试,持续攻坚,拥抱变化,主观能动性,独当一面,主动追踪业务目标
表述风格=强调技术创新为客户创造价值,采用量化指标证明成果,将公司价值观与实际工作结合
语言特点=积极向上,注重成果导向,体现责任担当,强调持续改进

[OKR结构模板]
# 固定的三大目标维度
目标1=AI催收 (权重40%)
目标1_描述=包括催收、监工、小结相关内容
目标1_KR数量=3个

目标2=AI私域 (权重30%)
目标2_描述=私域相关的所有内容
目标2_KR数量=3个

目标3=AI战役 (权重30%)
目标3_描述=包括金融事业部AI战役、分享、答疑等
目标3_KR数量=3个

[KR编写原则]
量化指标=必须包含具体的数字目标(准确率、响应时间、完成率等)
业务价值=每个KR都要体现对业务的实际贡献
技术突破=强调技术创新和优化成果
时间节点=明确完成时间要求

[价值观评估框架]
价值观1=简单(你简单,世界也简单)
价值观1_子项=就事论事言行一致真实不装,把简单留给客户把难题留给自己,直面问题报喜更要报忧

价值观2=快速(快比慢好,养成奔跑的习惯)
价值观2_子项=学练超CCC,有议有决有行动,为过程喝彩为结果买单

价值观3=突破(今天最高的表现是明天最低的要求)
价值观3_子项=先知先见先行争第一,不找借口只找方法,敢担当敢决断不留退路才有出路

价值观4=共赢(力出一孔,利出一孔)
价值观4_子项=要不断问自己为客户赢了什么,对自己比对别人狠,职责有分工心态无边界

[价值观案例结构]
案例格式=背景+我的行动+结果
背景要求=描述具体的项目挑战或问题
行动要求=详细说明采取的具体措施和方法
结果要求=用量化数据证明成果和价值

[常用量化指标]
准确率指标=80%+, 85%+, 90%+, 95%+
响应时间=3秒内, 4秒内
提升幅度=从X%提升至Y%
使用率=60%+, 98%
完成率=100%完成, 超额完成

[技术关键词库]
AI技术=大模型, 提示词工程, RAG召回, Agent, 流程编排
优化方向=准确率提升, 响应时间优化, 架构优化, 流程优化
业务场景=催收, 监工, 小结, 私域, 质检, 客服
技术手段=多模型协同, 知识库建设, 缓存优化, 并行处理

[项目分类]
AI催收项目=AI监工, 多维小结, 催收Agent, 触达系统
AI私域项目=金融私域Agent, 退保场景, 话术知识库
AI战役项目=技术分享, 跨部门协作, 子战役支撑

[自评等级标准]
A级标准=超额完成目标, 有重大技术突破, 业务价值显著
B级标准=完成既定目标, 技术方案可行, 业务效果良好
C级标准=基本完成目标, 存在改进空间

[常用表述模板]
开头模板=在过去的X中,我始终紧跟团队AIGC战役步伐,快步尝试,持续攻坚,拥抱变化
过程描述=通过X技术手段,实现了Y业务目标,取得了Z量化成果
结果总结=为X业务创造了实际价值,体现了Y价值观的践行
展望模板=在下X的规划中,我将更加注重提升主观能动性,努力独当一面,主动追踪业务目标

[注意事项]
避免内容=AI质检, AI智检, X线AI平台等非核心业务
重点突出=技术创新, 业务价值, 量化成果, 价值观践行
语言风格=积极正面, 数据导向, 价值观融入, 成果突出

[AI战役相关信息]
总owner=陆云飞
AI分享社区总管=雷雨
零售AI子战役owner=陆云飞
经纪AI子战役owner=王帆
保后AI子战役owner=何雯
风控AI子战役owner=王欣
数据AI子战役owner=郭育波
科技AI子战役owner=张佳彬

[OKR示例模板]
# AI催收目标示例
AI催收目标标题=支撑AI催收战役，完成核心技术突破与业务落地
AI催收KR1模板=完成[具体功能]升级，实现[指标名称][数值]%+
AI催收KR2模板=完成[技术方案]优化，实现[性能指标][具体数值]
AI催收KR3模板=支撑[业务场景]全流程[具体成果]

# AI私域目标示例
AI私域目标标题=推动AI私域场景深度落地，实现业务价值最大化
AI私域KR1模板=完成[Agent名称]优化升级，实现准确率[数值]%+
AI私域KR2模板=建立完善的[业务场景]知识库体系
AI私域KR3模板=支撑[业务名称]规模化应用

# AI战役目标示例
AI战役目标标题=支撑金融事业部AI战役，推动技术分享与社区建设
AI战役KR1模板=完成AI技术分享与社区总管职责
AI战役KR2模板=支撑金融事业部AI战役关键指标达成
AI战役KR3模板=推动AI技术创新与最佳实践沉淀

[价值观案例示例模板]
# 简单价值观案例模板
简单案例背景=[项目名称]项目初期，[具体问题描述]，[影响说明]
简单案例行动=1.直面问题，主动向[对象]汇报真实的[数据]，不回避[问题现实] 2.将复杂的[技术难题]全部内部消化 3.通过[技术手段]，将[性能指标]从[原值]优化至[新值]
简单案例结果=为[业务方]提供了[具体成果]，[业务指标]达到[数值]，[价值体现]

# 快速价值观案例模板
快速案例背景=[项目名称]需要在[时间限制]前完成[具体任务]，时间紧迫，涉及[技术挑战]
快速案例行动=1.快速制定"[时间计划]"的冲刺计划 2.采用[开发模式]，[具体做法] 3.与[合作方]并行开发，确保[关键功能]同步推进
快速案例结果=成功在[时间节点]完成[具体成果]，实现了[业务价值]

# 突破价值观案例模板
突破案例背景=[功能名称]需要同时支持[多个场景]，技术难度大，且要求[具体指标要求]
突破案例行动=1.主动承担[技术任务]，不等不靠，率先提出[技术方案] 2.面对[挑战]，采用[技术手段] 3.建立[机制名称]，针对[具体问题]逐一攻破
突破案例结果=成功实现[具体指标]，为[业务场景]提供了[服务价值]

# 共赢价值观案例模板
共赢案例背景=作为[角色名称]，需要统筹协调[多个部门/项目]的[具体工作]
共赢案例行动=1.主动承担[协调职责]，为[对象]提供[支持内容] 2.组织完成[具体活动]，将[技术经验]向[范围]推广 3.建立[机制名称]，及时响应[需求类型]
共赢案例结果=通过[协作方式]，有效推动了[业务成果]，为[整体目标]贡献了[具体价值]

[周报内容映射规则]
# 从周报提取OKR内容的规则
监工相关内容=归入AI催收目标，重点提取准确率、响应时间、异常监控能力等指标
小结相关内容=归入AI催收目标，重点提取标注准确率、耗时优化、业务赋能等成果
私域Agent内容=归入AI私域目标，重点提取准确率提升、功能开发、业务应用等
技术分享内容=归入AI战役目标，重点提取分享次数、协作成果、技术推广等
跨部门协作=归入AI战役目标，重点提取支撑的子战役数量和效果

# 从周报提取价值观案例的规则
技术难题解决=适合简单价值观，强调把复杂问题内化，给用户简单体验
紧急项目交付=适合快速价值观，强调快速响应和高效执行
技术创新突破=适合突破价值观，强调主动攻坚和创新方案
团队协作支撑=适合共赢价值观，强调跨部门协作和价值创造

[详细表述示例]
# 自评开头示例
自评开头1=在过去的[时间周期]中，我始终紧跟团队AIGC战役步伐，快步尝试，持续攻坚，拥抱变化，完成了既定目标，在各AIGC战役中都取得了显著成果。
自评开头2=在[时间周期]的工作中，我始终保持拥抱变化的心态，紧跟团队AI战役步伐，在[三大领域]都取得了显著成果。

# KR完成情况描述示例
KR完成描述1=通过[技术手段]，成功将[指标名称]从[原始数值]提升至[目标数值]，[业务价值描述]
KR完成描述2=完成[功能名称]的[具体工作]，实现[量化指标]，为[业务场景]提供了[价值贡献]
KR完成描述3=建立[系统/机制名称]，实现[具体能力]，[效果量化描述]

# 价值观总评示例
价值观总评模板=在[时间周期]的工作中，我始终将公司核心价值观贯穿于每一个项目实践中：[具体价值观践行描述]。通过这[时间]的实践，我不仅在技术能力上实现了新的突破，更重要的是将价值观真正融入到了工作的每一个环节中，为客户和公司创造了实实在在的价值。

# 业绩总评示例
业绩总评模板=在[时间周期]的工作中，我始终[价值观关键词]，在[三大核心领域]都取得了显著成果。回顾这[时间]的工作历程，我深感[技术发展感悟]。在[下个周期]的规划中，我将继续发挥主观能动性，努力在更多场景中独当一面，主动追踪业务目标，以更高的标准要求自己。

[使用说明]
1. 根据实际工作内容填充具体项目和数据
2. 按照三大目标维度组织OKR内容
3. 每个价值观选择1-2个最具代表性的案例
4. 确保所有数据真实可验证
5. 保持话术风格的一致性
6. 突出技术创新和业务价值的结合
7. 使用示例模板时，将[]内的占位符替换为具体内容
8. 从周报中按映射规则提取关键信息
9. 确保量化指标的准确性和可验证性
